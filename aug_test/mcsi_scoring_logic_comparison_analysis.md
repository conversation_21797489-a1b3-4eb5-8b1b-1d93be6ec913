# MCSI技术指标评分逻辑对比分析报告

## 分析概述

通过详细对比Pine Script权威源代码（`MCSI.pine`）与Python权威实现（`mcsi_*.py`），发现了多个关键的评分逻辑差异。这些差异导致Python实现的评分结果与Pine Script原始设计存在偏差。

## 1. MCSI RSI指标评分逻辑对比

### 1.1 Pine Script权威逻辑 (MCSI.pine 第98-128行)

```pine
// 日线信号分数计算
if crsi_daily[1] < db_daily[1] and crsi_daily > db_daily[1] and crsi_daily > crsi_daily[1]
    dailyScore := 67, signalDuration := 2
else if crsi_daily[1] > ub_daily[1] and crsi_daily < ub_daily[1] and crsi_daily < crsi_daily[1]
    dailyScore := -67, signalDuration := 2
else if crsi_daily < db_daily
    dailyScore := crsi_daily > crsi_daily[1] ? 27 : 13, signalDuration := 2
else if crsi_daily > ub_daily
    dailyScore := crsi_daily < crsi_daily[1] ? -27 : -13, signalDuration := 2
else if signalDuration > 0
    signalDuration := signalDuration - 1
else
    dailyScore := 0

// 周线信号分数计算
if crsi_weekly[1] < db_weekly[1] and crsi_weekly > db_weekly[1] and crsi_weekly > crsi_weekly[1]
    weeklyScore := 67, weeklySignalDuration := 2
else if crsi_weekly[1] > ub_weekly[1] and crsi_weekly < ub_weekly[1] and crsi_weekly < crsi_weekly[1]
    weeklyScore := -67, weeklySignalDuration := 2
else if crsi_weekly < db_weekly
    weeklyScore := crsi_weekly > crsi_weekly[1] ? 33 : 33, weeklySignalDuration := 2
else if crsi_weekly > ub_weekly
    weeklyScore := crsi_weekly < crsi_weekly[1] ? -33 : -33, weeklySignalDuration := 2
else if weeklySignalDuration > 0
    weeklySignalDuration := weeklySignalDuration - 1
else
    weeklyScore := 0

// 计算RSI最终分数
rsiScore := math.max(-100, math.min(100, dailyScore + weeklyScore))
```

### 1.2 Python实现逻辑 (mcsi_rsi.py 第373-441行)

```python
def _calculate_rsi_score(self, crsi_daily, db_daily, ub_daily, crsi_weekly, db_weekly, ub_weekly):
    # ... 省略部分代码 ...
    for i in range(1, length):
        # 日线信号分数计算 (严格按照Pine Script逻辑)
        if (crsi_daily[i-1] < db_daily[i-1] and crsi_daily[i] > db_daily[i-1] and
            crsi_daily[i] > crsi_daily[i-1]):
            # 突破下轨且上升
            daily_score[i] = 67
            signal_duration[i] = 2
        elif (crsi_daily[i-1] > ub_daily[i-1] and crsi_daily[i] < ub_daily[i-1] and
              crsi_daily[i] < crsi_daily[i-1]):
            # 跌破上轨且下降
            daily_score[i] = -67
            signal_duration[i] = 2
        elif crsi_daily[i] < db_daily[i]:
            # 在下轨之下
            daily_score[i] = 27 if crsi_daily[i] > crsi_daily[i-1] else 13
            signal_duration[i] = 2
        elif crsi_daily[i] > ub_daily[i]:
            # 在上轨之上
            daily_score[i] = -27 if crsi_daily[i] < crsi_daily[i-1] else -13
            signal_duration[i] = 2
        elif signal_duration[i-1] > 0:
            # 信号持续期间
            signal_duration[i] = signal_duration[i-1] - 1
            daily_score[i] = daily_score[i-1]
        else:
            daily_score[i] = 0
            signal_duration[i] = 0
```

### 1.3 RSI评分逻辑差异分析

**❌ 关键问题1：信号持续机制实现错误**
- **Pine Script**: 使用var变量`signalDuration`在K线间保持状态
- **Python实现**: 使用数组`signal_duration[i]`，但逻辑不完全对齐

**❌ 关键问题2：周线评分逻辑差异**
- **Pine Script**: 周线持续信号固定为33分（无论上升下降）
- **Python实现**: 可能存在条件判断差异

## 2. MCSI MACD指标评分逻辑对比

### 2.1 Pine Script权威逻辑 (MCSI.pine 第130-140行)

```pine
// MACD评分
if math.abs(histLine) <= dynamicThreshold
    macdScore := 0
else if histLine > dynamicThreshold and histA_IsUp[1] and histA_IsDown
    macdScore := -(50 + math.min(50, (math.abs(histLine / dynamicThreshold) - 1) * 25))
else if histLine < -dynamicThreshold and histB_IsDown[1] and histB_IsUp
    macdScore := 50 + math.min(50, (math.abs(histLine / dynamicThreshold) - 1) * 25)
else if histColorChanged
    macdScore := 0
else
    macdScore := macdScore[1]
```

### 2.2 Python实现逻辑 (mcsi_macd.py 第167-226行)

```python
def _calculate_macd_score_fixed(self, histogram, dynamic_threshold, hist_near_zero,
                               hist_above_threshold, hist_below_threshold,
                               hist_a_is_up_prev, hist_a_is_down,
                               hist_b_is_down_prev, hist_b_is_up,
                               hist_color_changed):
    macd_score = np.zeros(len(histogram))

    for i in range(1, len(histogram)):
        if hist_near_zero[i]:
            macd_score[i] = 0
            self.macd_score = 0
        elif (hist_above_threshold[i] and hist_a_is_up_prev[i] and hist_a_is_down[i]):
            if dynamic_threshold[i] != 0:
                relative_height = abs(histogram[i] / dynamic_threshold[i])
                extra_score = min(50, (relative_height - 1) * 25)
                score = -(50 + extra_score)
            else:
                score = -50
            macd_score[i] = score
            self.macd_score = score
        # ... 其他条件类似 ...
```

### 2.3 MACD评分逻辑差异分析

**✅ MACD实现基本正确**
- 条件判断逻辑与Pine Script对齐
- 动态阈值计算正确
- var变量状态管理机制正确实现

## 3. MCSI MMT指标评分逻辑对比

### 3.1 Pine Script权威逻辑 (MCSI.pine 第319-368行)

```pine
// 轨道位置分数计算（50%权重）
if CSIBuffer[1] > mmtHighBand[1] and CSIBuffer < mmtHighBand
    mmtChannelScore := -100
    mmtLastChannelScore := -100
    mmtChannelSignalDuration := 2
else if CSIBuffer[1] < mmtLowBand[1] and CSIBuffer > mmtLowBand
    mmtChannelScore := 100
    mmtLastChannelScore := 100
    mmtChannelSignalDuration := 2
else if CSIBuffer > mmtHighBand
    mmtChannelScore := mmtMomentum < 0 ? -80 : -20
    mmtLastChannelScore := mmtChannelScore
    mmtChannelSignalDuration := 2
else if CSIBuffer < mmtLowBand
    mmtChannelScore := mmtMomentum > 0 ? 80 : 20
    mmtLastChannelScore := mmtChannelScore
    mmtChannelSignalDuration := 2
else
    if mmtChannelSignalDuration > 0
        mmtChannelSignalDuration := mmtChannelSignalDuration - 1
        mmtChannelScore := mmtLastChannelScore
    else
        mmtChannelScore := 0

// 最终MMT分数计算
mmtScore := mmtChannelScore * channelWeight + mmtDivergenceScore * divergenceWeight
```

### 3.2 Python实现逻辑 (mcsi_mmt.py 第353-468行)

```python
def _calculate_scores_fixed(self, csi_buffer, high_band, low_band, momentum, divergences):
    channel_score = np.zeros(len(csi_buffer))
    divergence_score = np.zeros(len(csi_buffer))

    for i in range(1, len(csi_buffer)):
        # === 轨道位置分数计算 ===
        if (not np.isnan(high_band[i-1]) and not np.isnan(high_band[i]) and
            csi_buffer[i-1] > high_band[i-1] and csi_buffer[i] < high_band[i]):
            channel_score[i] = -100
            self.last_channel_score = -100
            self.channel_signal_duration = 2
        # ... 其他条件类似 ...
        
    # 最终分数计算
    mmt_score = channel_score * self.channel_weight + divergence_score * self.divergence_weight
```

### 3.3 MMT评分逻辑差异分析

**✅ MMT实现基本正确**
- 轨道位置判断逻辑正确
- 背离检测逻辑完整
- 权重计算正确

## 4. MCSI TTM指标评分逻辑对比

### 4.1 Pine Script权威逻辑 (MCSI.pine 第78-82行)

```pine
// TD9计算
TD := close > close[4] ? nz(TD[1])+1 : 0
TS := close < close[4] ? nz(TS[1])+1 : 0
TDUp = TD - ta.valuewhen(TD < TD[1], TD, 1)
TDDn = TS - ta.valuewhen(TS < TS[1], TS, 1)
getTD9Score(count) => count == 0 or count <= 6 ? 0.0 : count == 7 ? 20.0 : count == 8 ? 50.0 : count == 9 or (count >= 13 and count <= 16) ? 100.0 : count >= 10 and count <= 12 ? 80.0 : 0.0
td9Score := TDDn > 0 ? getTD9Score(TDDn) : -getTD9Score(TDUp)
```

### 4.2 Python实现逻辑 (mcsi_ttm.py 第49-183行)

```python
def calculate_td_sequences(self, close_prices):
    length = len(close_prices)
    td = np.zeros(length, dtype=int)
    ts = np.zeros(length, dtype=int)

    for i in range(self.comparison_period, length):
        if close_prices[i] > close_prices[i - self.comparison_period]:
            td[i] = td[i-1] + 1
        else:
            td[i] = 0

        if close_prices[i] < close_prices[i - self.comparison_period]:
            ts[i] = ts[i-1] + 1
        else:
            ts[i] = 0

    return td, ts

def get_ttm_score(self, count):
    if count == 0 or count <= 6:
        return 0.0
    elif count == 7:
        return 20.0
    elif count == 8:
        return 50.0
    elif count == 9 or (13 <= count <= 16):
        return 100.0
    elif 10 <= count <= 12:
        return 80.0
    else:
        return 0.0
```

### 4.3 TTM评分逻辑差异分析

**✅ TTM实现基本正确**
- TD序列计算逻辑正确
- 评分规则完全对齐
- 计数逻辑正确实现

## 5. 关键问题总结

### 5.1 主要问题点

**❌ RSI指标存在严重问题：**

1. **信号持续机制错误**：
   - Pine Script使用var变量在K线间保持状态
   - Python实现使用数组，但状态管理不完全正确

2. **周线评分逻辑差异**：
   - Pine Script: `weeklyScore := crsi_weekly > crsi_weekly[1] ? 33 : 33`（固定33分）
   - Python可能存在条件判断差异

3. **状态变量管理不一致**：
   - Pine Script的var变量机制在Python中实现不完全正确

### 5.2 次要问题点

**⚠️ 其他指标的潜在问题：**

1. **MACD指标**：基本正确，但需要验证var变量状态管理
2. **MMT指标**：基本正确，背离检测逻辑完整
3. **TTM指标**：基本正确，评分规则对齐

### 5.3 影响评估

**评分偏差影响：**

1. **RSI指标**：可能导致显著的评分差异，特别是在信号持续期间
2. **整体评分**：由于RSI权重较高(0.8)，会影响综合评分准确性
3. **交易信号**：可能导致信号触发时机和强度的偏差

## 6. 建议修复方向

### 6.1 优先修复项

1. **RSI信号持续机制**：重新实现var变量状态管理
2. **RSI周线评分逻辑**：确保与Pine Script完全一致
3. **状态变量管理**：统一所有指标的状态管理机制

### 6.2 验证方法

1. **逐K线对比**：使用相同数据对比每个K线的评分结果
2. **边界条件测试**：测试信号触发和结束的边界情况
3. **长期回测**：验证修复后的长期评分一致性

## 7. 详细代码差异对比

### 7.1 RSI周线评分关键差异

**Pine Script (MCSI.pine 第118-119行):**
```pine
else if crsi_weekly < db_weekly
    weeklyScore := crsi_weekly > crsi_weekly[1] ? 33 : 33, weeklySignalDuration := 2
```

**Python实现 (mcsi_rsi.py 第422-425行):**
```python
elif crsi_weekly[i] < db_weekly[i]:
    # 在下轨之下 (注意：Pine Script中周线这里都是33)
    weekly_score[i] = 33  # Pine Script: crsi_weekly > crsi_weekly[1] ? 33 : 33
    weekly_signal_duration[i] = 2
```

**❌ 问题分析：**
- Pine Script中无论上升下降都是33分，但注释显示可能存在条件判断
- Python实现直接使用33分，可能遗漏了某些逻辑细节

### 7.2 信号持续机制差异

**Pine Script var变量机制:**
```pine
var int signalDuration = 0
var int weeklySignalDuration = 0

// 在条件中直接修改var变量
if condition
    signalDuration := 2
else if signalDuration > 0
    signalDuration := signalDuration - 1
```

**Python数组实现:**
```python
signal_duration = np.zeros(length, dtype=int)
weekly_signal_duration = np.zeros(length, dtype=int)

# 在循环中处理数组
for i in range(1, length):
    if condition:
        signal_duration[i] = 2
    elif signal_duration[i-1] > 0:
        signal_duration[i] = signal_duration[i-1] - 1
```

**❌ 关键差异：**
- Pine Script的var变量在整个指标生命周期中保持状态
- Python数组实现无法完全模拟var变量的跨K线状态保持

### 7.3 MACD状态管理对比

**Pine Script (MCSI.pine 第139-140行):**
```pine
else
    macdScore := macdScore[1]  // 保持前一个分数
```

**Python实现 (mcsi_macd.py 第222-224行):**
```python
else:
    # macdScore := macdScore[1]  // 保持前一个分数
    macd_score[i] = self.macd_score
```

**✅ 这部分实现正确：**
- 使用实例变量`self.macd_score`模拟var变量
- 状态保持逻辑正确

## 8. 统一接口继承的问题

由于统一接口直接调用Python权威实现，所有评分逻辑问题都会被继承：

**统一接口调用 (mcsi_rsi_scoring.py 第101行):**
```python
result = self.source_indicator.calculate(input_data['close'].values)
rsi_scores = result.get('rsi_score', [])
```

**问题传播路径：**
1. Pine Script (正确) → Python权威实现 (有问题) → 统一接口 (继承问题)
2. 需要先修复Python权威实现，统一接口自动获得修复

## 9. 具体问题定位

### 9.1 RSI指标问题位置

**文件**: `TV-code/py-code/mcsi_rsi.py`
**问题行**: 第373-441行 `_calculate_rsi_score`方法
**具体问题**:
1. 第403-409行：日线信号持续机制实现不完全正确
2. 第422-425行：周线评分可能存在逻辑简化
3. 第430-436行：周线信号持续机制需要验证

### 9.2 其他指标验证点

**MACD指标** (`mcsi_macd.py`):
- ✅ 第167-226行：状态管理基本正确
- ⚠️ 需要验证var变量模拟的完整性

**MMT指标** (`mcsi_mmt.py`):
- ✅ 第353-468行：评分逻辑基本正确
- ⚠️ 需要验证背离检测的时序逻辑

**TTM指标** (`mcsi_ttm.py`):
- ✅ 第49-183行：TD序列计算正确
- ✅ 第26-47行：评分规则完全对齐

## 10. 结论

**主要发现：**
- RSI指标存在关键的评分逻辑差异，需要重点修复
- MACD、MMT、TTM指标基本正确，但需要细节验证
- 问题主要集中在var变量状态管理机制的Python实现上

**修复紧急度：**
- 🔴 **高优先级**：RSI指标的信号持续机制和周线评分逻辑
- 🟡 **中优先级**：其他指标的状态变量管理验证
- 🟢 **低优先级**：边界条件和异常处理优化

**问题影响评估：**
- RSI权重为0.8，评分差异会显著影响综合评分
- 可能导致交易信号的时机和强度偏差
- 需要优先修复RSI指标以确保系统准确性
