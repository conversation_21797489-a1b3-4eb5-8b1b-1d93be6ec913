#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI评分逻辑差异验证脚本
专门测试Python实现与Pine Script逻辑的具体差异点
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'TV-code' / 'py-code'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_rsi_signal_duration_mechanism():
    """测试RSI信号持续机制的具体问题"""
    logger.info("=== 测试RSI信号持续机制 ===")
    
    try:
        from mcsi_rsi import MCSIRSIIndicator
        
        # 创建特定的测试数据来触发信号持续机制
        # 模拟一个突破下轨然后回到中性区域的场景
        test_prices = np.array([
            100, 99, 98, 97, 96,  # 下跌趋势
            95, 94, 93, 92, 91,   # 继续下跌，触发下轨
            92, 93, 94, 95, 96,   # 反弹，测试信号持续
            97, 98, 99, 100, 101  # 回到正常区域
        ])
        
        rsi_indicator = MCSIRSIIndicator()
        result = rsi_indicator.calculate(test_prices)
        
        rsi_scores = result['rsi_score']
        daily_scores = result.get('daily_score', [])
        weekly_scores = result.get('weekly_score', [])
        
        logger.info("RSI信号持续机制测试结果:")
        logger.info(f"  总长度: {len(rsi_scores)}")
        logger.info(f"  非零评分数量: {np.sum(rsi_scores != 0)}")
        logger.info(f"  评分范围: [{np.min(rsi_scores):.2f}, {np.max(rsi_scores):.2f}]")
        
        # 检查信号持续性
        non_zero_indices = np.where(rsi_scores != 0)[0]
        if len(non_zero_indices) > 0:
            logger.info(f"  非零评分位置: {non_zero_indices}")
            logger.info(f"  对应评分值: {rsi_scores[non_zero_indices]}")
        
        # 分析日线和周线分数
        if len(daily_scores) > 0:
            logger.info(f"  日线分数范围: [{np.min(daily_scores):.2f}, {np.max(daily_scores):.2f}]")
        if len(weekly_scores) > 0:
            logger.info(f"  周线分数范围: [{np.min(weekly_scores):.2f}, {np.max(weekly_scores):.2f}]")
        
        return True
        
    except Exception as e:
        logger.error(f"RSI信号持续机制测试失败: {e}")
        return False

def test_rsi_weekly_scoring_logic():
    """测试RSI周线评分逻辑的具体问题"""
    logger.info("\n=== 测试RSI周线评分逻辑 ===")
    
    try:
        from mcsi_rsi import MCSIRSIIndicator
        
        # 创建周线数据测试场景
        # 模拟周线CRSI在下轨附近的不同运动方向
        weekly_test_data = {
            'close': np.array([100 + i * 0.1 for i in range(50)]),  # 缓慢上升
            'open': np.array([99.9 + i * 0.1 for i in range(50)]),
            'high': np.array([100.2 + i * 0.1 for i in range(50)]),
            'low': np.array([99.8 + i * 0.1 for i in range(50)]),
            'date': pd.date_range(start='2024-01-01', periods=50, freq='D')
        }
        
        rsi_indicator = MCSIRSIIndicator()
        result = rsi_indicator.calculate(weekly_test_data)
        
        weekly_scores = result.get('weekly_score', [])
        crsi_weekly = result.get('crsi_weekly', [])
        
        logger.info("RSI周线评分逻辑测试结果:")
        if len(weekly_scores) > 0:
            logger.info(f"  周线分数范围: [{np.min(weekly_scores):.2f}, {np.max(weekly_scores):.2f}]")
            logger.info(f"  周线分数中位数: {np.median(weekly_scores):.2f}")
            
            # 检查是否存在固定的33分评分
            score_33_count = np.sum(np.abs(weekly_scores - 33) < 0.01)
            score_minus_33_count = np.sum(np.abs(weekly_scores + 33) < 0.01)
            logger.info(f"  +33分出现次数: {score_33_count}")
            logger.info(f"  -33分出现次数: {score_minus_33_count}")
            
            # 分析Pine Script中的固定33分逻辑
            if score_33_count > 0 or score_minus_33_count > 0:
                logger.info("  ✅ 检测到Pine Script中的固定33分逻辑")
            else:
                logger.info("  ⚠️ 未检测到Pine Script中的固定33分逻辑")
        
        return True
        
    except Exception as e:
        logger.error(f"RSI周线评分逻辑测试失败: {e}")
        return False

def test_macd_var_variable_simulation():
    """测试MACD的var变量模拟机制"""
    logger.info("\n=== 测试MACD var变量模拟机制 ===")
    
    try:
        from mcsi_macd import MCSIMACDIndicator
        
        # 创建特定的MACD测试场景
        # 模拟柱状图颜色变化和阈值突破
        test_prices = np.array([
            100, 101, 102, 103, 104,  # 上升趋势
            105, 104, 103, 102, 101,  # 下降趋势
            100, 99, 98, 97, 96,      # 继续下降
            97, 98, 99, 100, 101      # 反弹
        ])
        
        macd_indicator = MCSIMACDIndicator()
        result = macd_indicator.calculate(test_prices)
        
        macd_scores = result['macd_score']
        histogram = result['histogram']
        dynamic_threshold = result['dynamic_threshold']
        
        logger.info("MACD var变量模拟测试结果:")
        logger.info(f"  评分范围: [{np.nanmin(macd_scores):.2f}, {np.nanmax(macd_scores):.2f}]")
        logger.info(f"  非零评分数量: {np.sum(macd_scores != 0)}")
        
        # 检查状态保持机制
        consecutive_same_scores = 0
        for i in range(1, len(macd_scores)):
            if macd_scores[i] == macd_scores[i-1] and macd_scores[i] != 0:
                consecutive_same_scores += 1
        
        logger.info(f"  连续相同非零评分次数: {consecutive_same_scores}")
        if consecutive_same_scores > 0:
            logger.info("  ✅ 检测到var变量状态保持机制")
        else:
            logger.info("  ⚠️ 未检测到明显的状态保持")
        
        # 检查动态阈值机制
        valid_thresholds = dynamic_threshold[~np.isnan(dynamic_threshold)]
        if len(valid_thresholds) > 0:
            logger.info(f"  动态阈值范围: [{np.min(valid_thresholds):.4f}, {np.max(valid_thresholds):.4f}]")
        
        return True
        
    except Exception as e:
        logger.error(f"MACD var变量模拟测试失败: {e}")
        return False

def test_mmt_divergence_detection():
    """测试MMT背离检测的准确性"""
    logger.info("\n=== 测试MMT背离检测准确性 ===")
    
    try:
        from mcsi_mmt import MCSIMMTIndicator
        
        # 创建明显的背离场景
        # 价格创新高，但指标不创新高（看跌背离）
        high_prices = np.array([100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110])
        low_prices = np.array([99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109])
        close_prices = np.array([99.5, 100.5, 101.5, 102.5, 103.5, 104.5, 105.5, 106.5, 107.5, 108.5, 109.5])
        
        mmt_indicator = MCSIMMTIndicator()
        result = mmt_indicator.calculate(close_prices, high_prices, low_prices)
        
        mmt_scores = result['mmt_score']
        bull_div = result.get('bull_div', [])
        bear_div = result.get('bear_div', [])
        hidden_bull_div = result.get('hidden_bull_div', [])
        hidden_bear_div = result.get('hidden_bear_div', [])
        
        logger.info("MMT背离检测测试结果:")
        logger.info(f"  评分范围: [{np.nanmin(mmt_scores):.2f}, {np.nanmax(mmt_scores):.2f}]")
        
        # 统计背离信号
        total_bull_div = np.sum(bull_div) if len(bull_div) > 0 else 0
        total_bear_div = np.sum(bear_div) if len(bear_div) > 0 else 0
        total_hidden_bull = np.sum(hidden_bull_div) if len(hidden_bull_div) > 0 else 0
        total_hidden_bear = np.sum(hidden_bear_div) if len(hidden_bear_div) > 0 else 0
        
        logger.info(f"  常规看涨背离: {total_bull_div}")
        logger.info(f"  常规看跌背离: {total_bear_div}")
        logger.info(f"  隐藏看涨背离: {total_hidden_bull}")
        logger.info(f"  隐藏看跌背离: {total_hidden_bear}")
        
        total_divergences = total_bull_div + total_bear_div + total_hidden_bull + total_hidden_bear
        if total_divergences > 0:
            logger.info("  ✅ 检测到背离信号")
        else:
            logger.info("  ⚠️ 未检测到背离信号（可能需要更多数据）")
        
        return True
        
    except Exception as e:
        logger.error(f"MMT背离检测测试失败: {e}")
        return False

def test_ttm_td_sequence_accuracy():
    """测试TTM TD序列计算的准确性"""
    logger.info("\n=== 测试TTM TD序列计算准确性 ===")
    
    try:
        from mcsi_ttm import MCSITTMIndicator
        
        # 创建明确的TD序列场景
        # 连续9个上涨，然后连续9个下跌
        test_prices = np.array([
            100, 100, 100, 100, 100,  # 前5个作为基准
            101, 102, 103, 104, 105,  # 连续5个上涨
            106, 107, 108, 109,       # 继续上涨到第9个
            108, 107, 106, 105, 104,  # 开始下跌
            103, 102, 101, 100, 99    # 继续下跌
        ])
        
        ttm_indicator = MCSITTMIndicator()
        result = ttm_indicator.calculate(test_prices)
        
        ttm_scores = result['ttm_score']
        td_up_count = result['td_up_count']
        td_down_count = result['td_down_count']
        
        logger.info("TTM TD序列计算测试结果:")
        logger.info(f"  评分范围: [{np.min(ttm_scores):.2f}, {np.max(ttm_scores):.2f}]")
        logger.info(f"  最大上升计数: {np.max(td_up_count)}")
        logger.info(f"  最大下降计数: {np.max(td_down_count)}")
        
        # 检查是否达到关键计数
        count_9_up = np.sum(td_up_count == 9)
        count_9_down = np.sum(td_down_count == 9)
        logger.info(f"  TD上升计数=9的次数: {count_9_up}")
        logger.info(f"  TD下降计数=9的次数: {count_9_down}")
        
        # 检查评分是否正确
        score_100 = np.sum(np.abs(ttm_scores) == 100)
        score_80 = np.sum(np.abs(ttm_scores) == 80)
        score_50 = np.sum(np.abs(ttm_scores) == 50)
        score_20 = np.sum(np.abs(ttm_scores) == 20)
        
        logger.info(f"  100分评分次数: {score_100}")
        logger.info(f"  80分评分次数: {score_80}")
        logger.info(f"  50分评分次数: {score_50}")
        logger.info(f"  20分评分次数: {score_20}")
        
        if count_9_up > 0 or count_9_down > 0:
            logger.info("  ✅ 成功检测到TD9信号")
        else:
            logger.info("  ⚠️ 未检测到TD9信号")
        
        return True
        
    except Exception as e:
        logger.error(f"TTM TD序列测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始MCSI评分逻辑差异验证")
    
    test_results = []
    
    # 执行各项差异测试
    test_results.append(('RSI信号持续机制', test_rsi_signal_duration_mechanism()))
    test_results.append(('RSI周线评分逻辑', test_rsi_weekly_scoring_logic()))
    test_results.append(('MACD var变量模拟', test_macd_var_variable_simulation()))
    test_results.append(('MMT背离检测', test_mmt_divergence_detection()))
    test_results.append(('TTM TD序列计算', test_ttm_td_sequence_accuracy()))
    
    # 汇总结果
    logger.info("\n" + "="*80)
    logger.info("MCSI评分逻辑差异验证结果汇总")
    logger.info("="*80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有差异测试通过！")
    else:
        logger.warning("⚠️ 部分测试失败，确认存在评分逻辑差异。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
