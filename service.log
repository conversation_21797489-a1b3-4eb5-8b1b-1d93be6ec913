ERROR:mcsi_rsi:真实周线数据计算失败: unconverted data remains when parsing with format "%Y-%m-%d": "2", at position 31. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
🔧 开发模式启动
📊 AI市场分析系统 v2.0 (Debug Mode)
==================================================
Warning: MCSI Premium units import failed: No module named 'core.scoring_units.mcsi_premium_units'
✅ 评分器初始化成功
🌐 启动开发服务器...
📱 本地访问: http://127.0.0.1:50505
📱 网络访问: http://**********:50505
🔄 热重载已启用
==================================================
 * Serving Flask app 'web.app'
 * Debug mode: on
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:50505
 * Running on http://**********:50505
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug: * Restarting with stat
ERROR:mcsi_rsi:真实周线数据计算失败: unconverted data remains when parsing with format "%Y-%m-%d": "2", at position 31. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 104-214-443
