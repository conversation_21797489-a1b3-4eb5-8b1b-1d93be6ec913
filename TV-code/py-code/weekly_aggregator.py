#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
周线数据聚合器
用于将日线数据聚合为周线数据
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_ohlc_data(self, data: Dict) -> bool:
        """
        验证OHLC数据
        
        Args:
            data: 包含OHLC数据的字典
            
        Returns:
            bool: 数据是否有效
        """
        try:
            required_fields = ['open', 'high', 'low', 'close']
            
            # 检查必需字段
            for field in required_fields:
                if field not in data:
                    self.logger.warning(f"缺少必需字段: {field}")
                    return False
            
            # 检查数据长度
            lengths = [len(data[field]) for field in required_fields]
            if not all(length == lengths[0] for length in lengths):
                self.logger.warning("OHLC数据长度不一致")
                return False
            
            # 检查数据有效性
            for field in required_fields:
                if not isinstance(data[field], (list, np.ndarray)):
                    self.logger.warning(f"字段 {field} 不是有效的数组类型")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False


class WeeklyDataAggregator:
    """周线数据聚合器"""
    
    def __init__(self, market_type: str = "stock"):
        """
        初始化周线数据聚合器
        
        Args:
            market_type: 市场类型，默认"stock"
        """
        self.market_type = market_type
        self.logger = logging.getLogger(__name__)
    
    def aggregate_to_weekly(self, daily_df: pd.DataFrame) -> pd.DataFrame:
        """
        将日线数据聚合为周线数据
        
        Args:
            daily_df: 日线数据DataFrame
            
        Returns:
            pd.DataFrame: 周线数据DataFrame
        """
        try:
            # 确保日期列存在
            if 'date' not in daily_df.columns:
                self.logger.warning("缺少日期列，使用索引作为日期")
                daily_df = daily_df.copy()
                daily_df['date'] = pd.date_range(start='2024-01-01', periods=len(daily_df), freq='D')
            
            # 转换日期列
            daily_df = daily_df.copy()
            daily_df['date'] = pd.to_datetime(daily_df['date'])
            daily_df.set_index('date', inplace=True)
            
            # 按周聚合
            weekly_df = daily_df.resample('W').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum' if 'volume' in daily_df.columns else lambda x: 1
            }).dropna()
            
            # 重置索引
            weekly_df.reset_index(inplace=True)
            
            self.logger.debug(f"聚合完成: {len(daily_df)}日 -> {len(weekly_df)}周")
            
            return weekly_df
            
        except Exception as e:
            self.logger.error(f"周线聚合失败: {str(e)}")
            # 返回空DataFrame
            return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume'])
    
    def _map_weekly_to_daily(self, weekly_df: pd.DataFrame, daily_df: pd.DataFrame, 
                           weekly_values: np.ndarray) -> np.ndarray:
        """
        将周线数据映射回日线时间序列
        
        Args:
            weekly_df: 周线数据DataFrame
            daily_df: 日线数据DataFrame
            weekly_values: 周线数值数组
            
        Returns:
            np.ndarray: 映射到日线的数值数组
        """
        try:
            daily_values = np.full(len(daily_df), np.nan)
            
            # 确保日期列存在
            if 'date' not in daily_df.columns:
                daily_dates = pd.date_range(start='2024-01-01', periods=len(daily_df), freq='D')
            else:
                daily_dates = pd.to_datetime(daily_df['date'])
            
            weekly_dates = pd.to_datetime(weekly_df['date'])
            
            # 为每个日线数据点找到对应的周线值
            for i, daily_date in enumerate(daily_dates):
                # 找到最近的周线日期
                week_idx = None
                for j, weekly_date in enumerate(weekly_dates):
                    if daily_date <= weekly_date:
                        week_idx = j
                        break
                
                if week_idx is not None and week_idx < len(weekly_values):
                    daily_values[i] = weekly_values[week_idx]
            
            return daily_values
            
        except Exception as e:
            self.logger.error(f"周线到日线映射失败: {str(e)}")
            return np.full(len(daily_df), np.nan)
