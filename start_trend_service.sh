#!/bin/bash
# 启动趋势分析服务

echo "🔍 启动趋势分析服务..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装或不在PATH中"
    exit 1
fi

# 检查是否已有服务在运行
if pgrep -f "trend_service.py" > /dev/null; then
    echo "⚠️  趋势分析服务已在运行"
    echo "如需重启，请先运行: ./stop_trend_service.sh"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动服务
echo "📡 启动趋势分析服务 (端口 5008)..."
nohup python3 trend_service.py > logs/trend_service.log 2>&1 &

# 等待服务启动
sleep 3

# 检查服务是否启动成功
if pgrep -f "trend_service.py" > /dev/null; then
    echo "✅ 趋势分析服务启动成功！"
    echo ""
    echo "📋 服务信息:"
    echo "  🌐 Web界面: http://localhost:5008"
    echo "  📊 API接口: http://localhost:5008/api/"
    echo ""
    echo "📊 查看日志:"
    echo "  tail -f logs/trend_service.log"
    echo ""
    echo "🛑 停止服务:"
    echo "  ./stop_trend_service.sh"
else
    echo "❌ 趋势分析服务启动失败"
    echo "请查看日志: cat logs/trend_service.log"
    exit 1
fi
