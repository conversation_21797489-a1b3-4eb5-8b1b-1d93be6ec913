[2025-08-21 11:18:20] INFO - 启动趋势分析服务...
[2025-08-21 11:18:20] INFO - 趋势分析器初始化成功
[2025-08-21 11:18:20] INFO - 趋势分析服务启动在端口 5008
 * Serving Flask app 'trend_service'
 * Debug mode: on
[2025-08-21 11:18:20] INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5008
 * Running on http://**********:5008
[2025-08-21 11:18:20] INFO - [33mPress CTRL+C to quit[0m
[2025-08-21 11:18:20] INFO -  * Restarting with stat
[2025-08-21 11:18:20] INFO - 启动趋势分析服务...
[2025-08-21 11:18:20] INFO - 趋势分析器初始化成功
[2025-08-21 11:18:20] INFO - 趋势分析服务启动在端口 5008
[2025-08-21 11:18:20] WARNING -  * Debu<PERSON> is active!
[2025-08-21 11:18:20] INFO -  * Debugger PIN: 104-214-443
[2025-08-21 11:21:37] INFO -  * Detected change in '/home/<USER>/Analyze-system2/web/app.py', reloading
[2025-08-21 11:21:37] INFO -  * Restarting with stat
[2025-08-21 11:21:37] INFO - 启动趋势分析服务...
[2025-08-21 11:21:37] INFO - 趋势分析器初始化成功
[2025-08-21 11:21:37] INFO - 趋势分析服务启动在端口 5008
[2025-08-21 11:21:37] WARNING -  * Debugger is active!
[2025-08-21 11:21:37] INFO -  * Debugger PIN: 104-214-443
[2025-08-21 11:27:53] INFO - 127.0.0.1 - - [21/Aug/2025 11:27:53] "GET / HTTP/1.1" 200 -
