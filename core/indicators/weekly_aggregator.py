"""
周线数据聚合器
用于将日线数据正确聚合为周线数据，以提高MCSI指标的准确性
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, Tuple


class WeeklyDataAggregator:
    """
    多市场周线数据聚合器
    支持股票、期货、加密货币等不同市场的周线聚合
    """

    # 市场类型常量
    MARKET_STOCK = "stock"          # 股票市场 (T+1, 5×24)
    MARKET_FUTURES = "futures"      # 期货市场 (部分夜盘)
    MARKET_CRYPTO = "crypto"        # 加密货币 (7×24)

    def __init__(self, market_type: str = MARKET_STOCK, timezone: str = "Asia/Shanghai"):
        """
        初始化周线数据聚合器

        Args:
            market_type: 市场类型 ('stock', 'futures', 'crypto')
            timezone: 时区设置，默认'Asia/Shanghai'
        """
        self.market_type = market_type
        self.timezone = timezone
        self.logger = logging.getLogger(__name__)

        # 根据市场类型设置聚合规则
        self._setup_aggregation_rules()

    def _setup_aggregation_rules(self):
        """根据市场类型设置聚合规则"""
        if self.market_type == self.MARKET_STOCK:
            # 股票市场：周一到周五，以周五结束
            self.resample_rule = 'W-FRI'
            self.trading_days = [0, 1, 2, 3, 4]  # 周一到周五
            self.session_start = "09:30"
            self.session_end = "15:00"

        elif self.market_type == self.MARKET_FUTURES:
            # 期货市场：包含夜盘，以周五结束
            self.resample_rule = 'W-FRI'
            self.trading_days = [0, 1, 2, 3, 4]  # 周一到周五
            self.session_start = "21:00"  # 夜盘开始
            self.session_end = "15:00"    # 日盘结束
            self.has_night_session = True

        elif self.market_type == self.MARKET_CRYPTO:
            # 加密货币：7×24交易，以周日结束
            self.resample_rule = 'W-SUN'
            self.trading_days = [0, 1, 2, 3, 4, 5, 6]  # 全周
            self.session_start = "00:00"
            self.session_end = "23:59"

        else:
            # 默认使用股票市场规则
            self.logger.warning(f"未知市场类型 {self.market_type}，使用股票市场规则")
            self.market_type = self.MARKET_STOCK
            self._setup_aggregation_rules()

    def aggregate_to_weekly(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """
        将日线数据聚合为周线数据（支持多种市场类型）

        Args:
            daily_data: 包含date, open, high, low, close, volume列的日线数据

        Returns:
            周线数据DataFrame
        """
        try:
            # 确保date列是datetime类型
            if 'date' not in daily_data.columns:
                raise ValueError("数据必须包含date列")

            daily_data = daily_data.copy()
            daily_data['date'] = pd.to_datetime(daily_data['date'])

            # 根据市场类型进行特殊处理
            if self.market_type == self.MARKET_CRYPTO:
                # 加密货币：7×24交易，需要特殊处理
                processed_data = self._process_crypto_data(daily_data)
            elif self.market_type == self.MARKET_FUTURES:
                # 期货：处理夜盘数据
                processed_data = self._process_futures_data(daily_data)
            else:
                # 股票：标准处理
                processed_data = self._process_stock_data(daily_data)

            processed_data = processed_data.set_index('date')

            # 按周聚合
            weekly_data = processed_data.resample(self.resample_rule).agg({
                'open': 'first',    # 周开盘价：第一个交易日的开盘价
                'high': 'max',      # 周最高价：一周内的最高价
                'low': 'min',       # 周最低价：一周内的最低价
                'close': 'last',    # 周收盘价：最后一个交易日的收盘价
                'volume': 'sum' if 'volume' in processed_data.columns else lambda x: len(x)  # 周成交量
            }).dropna()

            # 重置索引，将date作为列
            weekly_data = weekly_data.reset_index()

            self.logger.debug(f"{self.market_type}市场日线数据聚合为周线：{len(daily_data)} -> {len(weekly_data)}")

            return weekly_data

        except Exception as e:
            self.logger.error(f"周线数据聚合失败: {e}")
            raise

    def _process_stock_data(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """处理股票市场数据（T+1, 5×24）"""
        # 过滤交易日（周一到周五）
        daily_data['weekday'] = daily_data['date'].dt.weekday
        trading_data = daily_data[daily_data['weekday'].isin(self.trading_days)].copy()

        # 处理节假日（这里可以扩展节假日逻辑）
        # TODO: 可以添加节假日数据库查询

        return trading_data.drop('weekday', axis=1)

    def _process_futures_data(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """处理期货市场数据（部分夜盘）"""
        # 期货夜盘处理：夜盘时间跨越自然日
        daily_data['weekday'] = daily_data['date'].dt.weekday
        trading_data = daily_data[daily_data['weekday'].isin(self.trading_days)].copy()

        # 夜盘数据调整：将夜盘数据归入下一个交易日
        # 这里简化处理，实际应用中需要根据具体合约的夜盘时间调整
        if hasattr(self, 'has_night_session') and self.has_night_session:
            # 如果有时间信息，可以进行更精确的夜盘处理
            # 目前保持简化处理
            pass

        return trading_data.drop('weekday', axis=1)

    def _process_crypto_data(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """处理加密货币数据（7×24）"""
        # 加密货币全天候交易，不需要过滤交易日
        # 但需要确保按自然周聚合
        return daily_data

    def calculate_weekly_rsi(self, daily_data: pd.DataFrame, period: int = 14) -> np.ndarray:
        """
        基于真实周线数据计算RSI
        
        Args:
            daily_data: 日线数据
            period: RSI周期
            
        Returns:
            周线RSI数组（映射回日线长度）
        """
        try:
            # 聚合为周线数据
            weekly_data = self.aggregate_to_weekly(daily_data)
            
            if len(weekly_data) < period + 1:
                self.logger.warning(f"周线数据不足，无法计算RSI：需要{period+1}周，实际{len(weekly_data)}周")
                return np.full(len(daily_data), np.nan)
            
            # 计算周线RSI
            close_prices = weekly_data['close'].values
            rsi = self._calculate_rsi(close_prices, period)
            
            # 将周线RSI映射回日线时间序列
            daily_rsi = self._map_weekly_to_daily(weekly_data, daily_data, rsi)
            
            return daily_rsi
            
        except Exception as e:
            self.logger.error(f"周线RSI计算失败: {e}")
            return np.full(len(daily_data), np.nan)
    
    def _calculate_rsi(self, prices: np.ndarray, period: int) -> np.ndarray:
        """
        计算RSI指标
        
        Args:
            prices: 价格数组
            period: RSI周期
            
        Returns:
            RSI数组
        """
        if len(prices) < period + 1:
            return np.full(len(prices), np.nan)
            
        # 计算价格变化
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        # 初始化数组
        avg_gains = np.zeros(len(prices))
        avg_losses = np.zeros(len(prices))
        rsi = np.full(len(prices), np.nan)
        
        # 计算初始平均值
        if len(gains) >= period:
            avg_gains[period] = np.mean(gains[:period])
            avg_losses[period] = np.mean(losses[:period])
            
            # 计算后续的指数移动平均
            for i in range(period + 1, len(prices)):
                avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
                avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period
        
        # 计算RSI
        for i in range(period, len(prices)):
            if avg_losses[i] != 0:
                rs = avg_gains[i] / avg_losses[i]
                rsi[i] = 100 - (100 / (1 + rs))
            else:
                rsi[i] = 100 if avg_gains[i] > 0 else 50
                
        return rsi
    
    def _map_weekly_to_daily(self, weekly_data: pd.DataFrame, daily_data: pd.DataFrame,
                           weekly_values: np.ndarray) -> np.ndarray:
        """
        将周线数值映射回日线时间序列（改进版）

        Args:
            weekly_data: 周线数据
            daily_data: 日线数据
            weekly_values: 周线数值数组

        Returns:
            映射后的日线数值数组
        """
        try:
            daily_values = np.full(len(daily_data), np.nan)

            # 确保daily_data有date列
            if 'date' not in daily_data.columns:
                daily_dates = daily_data.index
            else:
                daily_dates = pd.to_datetime(daily_data['date'])

            # 改进的映射策略：基于周线聚合的实际逻辑
            if len(weekly_data) == 0 or len(weekly_values) == 0:
                return daily_values

            # 为每个周线数据点找到对应的日线范围
            for i, weekly_date in enumerate(weekly_data['date']):
                if i >= len(weekly_values) or pd.isna(weekly_values[i]):
                    continue

                week_end = pd.to_datetime(weekly_date)

                # 根据市场类型确定周的开始时间
                if self.market_type == self.MARKET_CRYPTO:
                    # 加密货币：自然周（周一到周日）
                    week_start = week_end - pd.Timedelta(days=6)
                else:
                    # 股票/期货：交易周（周一到周五）
                    week_start = week_end - pd.Timedelta(days=4)

                # 找到这一周的所有日线数据
                if isinstance(daily_dates, pd.Index):
                    week_mask = (daily_dates >= week_start) & (daily_dates <= week_end)
                else:
                    week_mask = (daily_dates >= week_start) & (daily_dates <= week_end)

                # 将周线数值赋给这一周的所有日线
                if np.any(week_mask):
                    daily_values[week_mask] = weekly_values[i]

            # 前向填充：如果前面的数据没有被映射，使用最近的有效值
            last_valid_value = None
            for i in range(len(daily_values)):
                if not pd.isna(daily_values[i]):
                    last_valid_value = daily_values[i]
                elif last_valid_value is not None:
                    daily_values[i] = last_valid_value

            return daily_values

        except Exception as e:
            self.logger.error(f"周线到日线映射失败: {e}")
            return np.full(len(daily_data), np.nan)
    
    def calculate_weekly_bands(self, daily_data: pd.DataFrame, period: int = 34, 
                             leveling: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于真实周线数据计算轨道
        
        Args:
            daily_data: 日线数据
            period: 轨道计算周期
            leveling: 水平化参数
            
        Returns:
            (上轨, 下轨) 映射回日线长度
        """
        try:
            # 聚合为周线数据
            weekly_data = self.aggregate_to_weekly(daily_data)
            
            if len(weekly_data) < period:
                self.logger.warning(f"周线数据不足，无法计算轨道：需要{period}周，实际{len(weekly_data)}周")
                return np.full(len(daily_data), np.nan), np.full(len(daily_data), np.nan)
            
            # 首先需要计算周线的CRSI
            close_prices = weekly_data['close'].values
            weekly_rsi = self._calculate_rsi(close_prices, 14)  # 使用14周期计算RSI
            
            # 计算周线轨道
            upper_band, lower_band = self._calculate_bands(weekly_rsi, period, leveling)
            
            # 将周线轨道映射回日线时间序列
            daily_upper = self._map_weekly_to_daily(weekly_data, daily_data, upper_band)
            daily_lower = self._map_weekly_to_daily(weekly_data, daily_data, lower_band)
            
            return daily_upper, daily_lower
            
        except Exception as e:
            self.logger.error(f"周线轨道计算失败: {e}")
            return np.full(len(daily_data), np.nan), np.full(len(daily_data), np.nan)
    
    def _calculate_bands(self, rsi_values: np.ndarray, period: int, leveling: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算轨道（基于Pine Script的banding函数）
        
        Args:
            rsi_values: RSI数值数组
            period: 计算周期
            leveling: 水平化参数（百分比）
            
        Returns:
            (上轨, 下轨)
        """
        upper_band = np.full(len(rsi_values), np.nan)
        lower_band = np.full(len(rsi_values), np.nan)
        
        percent = leveling / 100.0
        
        for i in range(period - 1, len(rsi_values)):
            # 获取当前周期的数据
            window_data = rsi_values[i - period + 1:i + 1]
            valid_data = window_data[~np.isnan(window_data)]
            
            if len(valid_data) < period * 0.8:  # 至少要有80%的有效数据
                continue
                
            maxima = np.max(valid_data)
            minima = np.min(valid_data)
            
            if maxima == minima:
                upper_band[i] = maxima
                lower_band[i] = minima
                continue
            
            mstep = (maxima - minima) / 100
            
            # 计算下轨
            for steps in range(101):
                testvalue = minima + mstep * steps
                below_count = np.sum(valid_data < testvalue)
                if below_count / len(valid_data) >= percent:
                    lower_band[i] = testvalue
                    break
            
            # 计算上轨
            for steps in range(101):
                testvalue = maxima - mstep * steps
                above_count = np.sum(valid_data >= testvalue)
                if above_count / len(valid_data) >= percent:
                    upper_band[i] = testvalue
                    break
        
        return upper_band, lower_band


class DataValidator:
    """数据一致性验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_ohlc_data(self, data: Dict) -> bool:
        """验证OHLC数据的一致性"""
        try:
            open_prices = np.array(data['open'])
            high_prices = np.array(data['high'])
            low_prices = np.array(data['low'])
            close_prices = np.array(data['close'])
            
            # 检查基本约束：high >= max(open, close), low <= min(open, close)
            for i in range(len(open_prices)):
                if high_prices[i] < max(open_prices[i], close_prices[i]):
                    self.logger.warning(f"数据异常：第{i}行高价小于开盘价或收盘价")
                    return False
                if low_prices[i] > min(open_prices[i], close_prices[i]):
                    self.logger.warning(f"数据异常：第{i}行低价大于开盘价或收盘价")
                    return False
                    
            self.logger.info("OHLC数据验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return False
    
    def get_data_summary(self, data: Dict) -> Dict:
        """获取数据摘要信息"""
        try:
            return {
                'data_points': len(data['close']),
                'date_range': f"{data['date'][0]} to {data['date'][-1]}" if 'date' in data else 'Unknown',
                'price_range': f"{min(data['low']):.2f} to {max(data['high']):.2f}",
                'avg_volume': np.mean(data['volume']) if 'volume' in data else 'N/A',
                'validation_status': 'basic_check_passed'
            }
        except Exception as e:
            self.logger.error(f"数据摘要生成失败: {e}")
            return {'error': str(e)}
